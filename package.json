{"name": "finwise", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.1.8", "@next/third-parties": "^14.2.13", "clsx": "^2.1.1", "framer-motion": "^11.11.9", "next": "^14.2.13", "react": "^18", "react-dom": "^18", "react-icons": "^5.3.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.13", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}