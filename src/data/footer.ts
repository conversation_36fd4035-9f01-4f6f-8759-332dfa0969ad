import { IMenuItem, ISocials } from "@/types";

export const footerDetails: {
  subheading: string;
  quickLinks: IMenuItem[];
  email: string;
  telephone: string;
  socials: ISocials;
} = {
  subheading: "JLPT対応の日本語学習アプリ。N5からN1まで段階的に同じ内容を比較学習できます。",
  quickLinks: [
    {
      text: "Features",
      url: "#features",
    },
    // {
    //   text: "Pricing",
    //   url: "#pricing",
    // },
    // {
    //   text: "Testimonials",
    //   url: "#testimonials",
    // },
  ],
  // email: "<EMAIL>",
  //   telephone: "",
  socials: {
    // github: 'https://github.com',
    x: "https://twitter.com/interjc",
    // twitter: "https://x.com/interjc",
    // facebook: "https://facebook.com",
    // youtube: 'https://youtube.com',
    // linkedin: "https://www.linkedin.com",
    // threads: 'https://www.threads.net',
    // instagram: "https://www.instagram.com",
  },
};
